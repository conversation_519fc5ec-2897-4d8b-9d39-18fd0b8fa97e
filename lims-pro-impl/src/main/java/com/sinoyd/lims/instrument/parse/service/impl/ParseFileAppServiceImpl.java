package com.sinoyd.lims.instrument.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.instrument.parse.dto.DtoParseFileApp;
import com.sinoyd.lims.instrument.parse.repository.ParseFileAppRepository;
import com.sinoyd.lims.instrument.parse.service.ParseFileAppService;
import dm.jdbc.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import redis.clients.jedis.JedisPoolConfig;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;


/**
 * FileApp操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/11/22
 * @since V100R001
 */
 @Service
 @Slf4j
public class ParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoParseFileApp,String, ParseFileAppRepository> implements ParseFileAppService {

     @Value("${fileProps.instrumentParseFilePath}")
     private String filePath;

     @Value("${instrument-parse.redis-connectStr:redis://127.0.0.1:6379/0}")
     private String instrumentRedisConnectStr;

    @Override
    public Boolean upload(String appId, HttpServletRequest request) {
        if (StringUtil.isEmpty(appId)) {
            throw new BaseException("应用配置丢失,请刷新数据");
        }
        DtoParseFileApp app = repository.findOne(appId);
        if (StringUtil.isNull(app)) {
           throw new BaseException("应用配置丢失,请刷新数据");
        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        String dirPath = app.getFolderName();
        if (StringUtil.isEmpty(dirPath)) {
            throw new BaseException("文档路径配置错误");
        }
        // 去除前后/
        dirPath = this.suitFolder(dirPath);
        File fileDir = new File(filePath + "/" + dirPath);
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        for (MultipartFile file : files) {
            InputStream inputStream = null;
            Path path = Paths.get(filePath + "/" + dirPath + "/"
                    + file.getOriginalFilename());
            try {
                Files.deleteIfExists(path);
                inputStream = file.getInputStream();
                Files.copy(inputStream, path);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new BaseException("上传失败");
            }finally {
                FileUtil.close(inputStream);
            }
        }
        return true;
    }

    /**
     * 创建仪器解析Redis连接实例
     *
     * @return 仪器解析专用的RedisTemplate实例
     */
    public synchronized RedisTemplate<String, Object> createInsParseRedisTemplate(){
        try {
            // 解析Redis连接字符串
            URI redisUri = URI.create(instrumentRedisConnectStr);
            String host = redisUri.getHost();
            int port = redisUri.getPort();
            String password = null;
            int database = 0;

            // 解析密码（如果存在）
            String userInfo = redisUri.getUserInfo();
            if (StringUtil.isNotEmpty(userInfo) && userInfo.contains(":")) {
                password = userInfo.split(":")[1];
            }

            // 解析数据库编号
            String path = redisUri.getPath();
            if (StringUtil.isNotEmpty(path) && path.length() > 1) {
                try {
                    database = Integer.parseInt(path.substring(1));
                } catch (NumberFormatException e) {
                    log.warn("解析Redis数据库编号失败，使用默认值0: {}", e.getMessage());
                }
            }

            // 创建Jedis连接池配置
            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(8);
            poolConfig.setMaxIdle(8);
            poolConfig.setMinIdle(0);
            poolConfig.setMaxWaitMillis(-1);
            poolConfig.setTestOnBorrow(false);
            poolConfig.setTestOnReturn(false);
            poolConfig.setTestWhileIdle(true);

            // 创建Jedis连接工厂
            JedisConnectionFactory connectionFactory = new JedisConnectionFactory(poolConfig);
            connectionFactory.setHostName(host);
            connectionFactory.setPort(port);
            connectionFactory.setDatabase(database);
            connectionFactory.setTimeout(100000);

            if (StringUtil.isNotEmpty(password)) {
                connectionFactory.setPassword(password);
            }

            // 初始化连接工厂
            connectionFactory.afterPropertiesSet();

            // 创建RedisTemplate
            RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
            redisTemplate.setConnectionFactory(connectionFactory);

            // 设置序列化器
            StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
            GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer();

            // 设置key和hashKey的序列化器
            redisTemplate.setKeySerializer(stringRedisSerializer);
            redisTemplate.setHashKeySerializer(stringRedisSerializer);

            // 设置value和hashValue的序列化器
            redisTemplate.setValueSerializer(jsonRedisSerializer);
            redisTemplate.setHashValueSerializer(jsonRedisSerializer);

            // 初始化RedisTemplate
            redisTemplate.afterPropertiesSet();

            log.info("仪器解析Redis连接实例创建成功，连接地址: {}:{}, 数据库: {}", host, port, database);
            return redisTemplate;

        } catch (Exception e) {
            log.error("创建仪器解析Redis连接实例失败: {}", e.getMessage(), e);
            throw new BaseException("创建仪器解析Redis连接实例失败: " + e.getMessage());
        }
    }


    /**
     * 使用示例：演示如何使用Redis连接实例
     *
     * @return 操作结果
     */
    public String demonstrateRedisUsage() {
        try {
            // 方式1：使用配置文件中的仪器解析Redis连接
            RedisTemplate<String, Object> insParseRedis = createInsParseRedisTemplate();
            insParseRedis.opsForValue().set("test:ins:parse", "仪器解析Redis测试数据");
            String insParseValue = (String) insParseRedis.opsForValue().get("test:ins:parse");
            log.info("仪器解析Redis测试结果: {}", insParseValue);

            // 方式2：使用自定义参数创建Redis连接
            RedisTemplate<String, Object> customRedis = createCustomRedisTemplate("192.168.1.100", 6379, "password123", 2);
            customRedis.opsForValue().set("test:custom", "自定义Redis测试数据");
            String customValue = (String) customRedis.opsForValue().get("test:custom");
            log.info("自定义Redis测试结果: {}", customValue);

            // 方式3：使用连接字符串创建Redis连接
            RedisTemplate<String, Object> connectStrRedis = createRedisTemplateByConnectStr("redis://password123@192.168.1.200:6379/3");
            connectStrRedis.opsForValue().set("test:connectstr", "连接字符串Redis测试数据");
            String connectStrValue = (String) connectStrRedis.opsForValue().get("test:connectstr");
            log.info("连接字符串Redis测试结果: {}", connectStrValue);

            return "Redis连接实例使用演示完成";

        } catch (Exception e) {
            log.error("Redis使用演示失败: {}", e.getMessage(), e);
            return "Redis使用演示失败: " + e.getMessage();
        }
    }

    /**
     * 去除前后/
     * @param folder 路径
     * @return 路径
     */
    private String suitFolder(String folder) {
        if (StringUtil.isEmpty(folder)) {
            return null;
        }
        if (folder.startsWith("/")) {
            folder = folder.substring(folder.indexOf("/") + 1);
        }
        if (folder.endsWith("/")) {
            folder = folder.substring(0, folder.lastIndexOf("/"));
        }
        return folder;
    }
}