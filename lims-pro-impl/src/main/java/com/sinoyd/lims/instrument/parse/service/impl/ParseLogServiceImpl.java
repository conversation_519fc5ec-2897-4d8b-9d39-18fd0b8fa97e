package com.sinoyd.lims.instrument.parse.service.impl;

import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.instrument.parse.dto.DtoParseFileApp;
import com.sinoyd.lims.instrument.parse.dto.DtoParseLog;
import com.sinoyd.lims.instrument.parse.repository.ParseFileAppRepository;
import com.sinoyd.lims.instrument.parse.repository.ParseLogRepository;
import com.sinoyd.lims.instrument.parse.service.ParseLogService;
import dm.jdbc.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仪器解析日志业务接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/09/03
 */
@Service
@Slf4j
public class ParseLogServiceImpl extends BaseJpaServiceImpl<DtoParseLog, String, ParseLogRepository> implements ParseLogService {

    @PersistenceContext(unitName = IBaseConstants.INSTRUMENT_PARSE_PERSISTENCE_UNIT)
    private EntityManager entityManager;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private ParseFileAppRepository parseFileAppRepository;

    @Value("${fileProps.instrumentParseFilePath}")
    private String filePath;

    @Autowired
    private UserService userService;

    @Override
    public void findByPage(PageBean<DtoParseLog> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoParseLog l, DtoParseFileApp p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select new com.sinoyd.lims.instrument.parse.dto.DtoParseLog(l.id, l.fileOrgName, p.instrumentName, p.instrumentCode, l.beginTime, l.parseStatus, l.creator) ");
        commonRepository.findByPage(entityManager, page, criteria);
        List<DtoParseLog> pageData = page.getData();
        List<String> creatorIds = pageData.stream().map(DtoParseLog::getCreator).distinct().collect(Collectors.toList());
        // 获取操作人id，列表冗余操作人姓名
        List<DtoUser> userAllList = StringUtil.isNotEmpty(creatorIds) ? userService.findByIds(creatorIds) : new ArrayList<>();
        Map<String, String> userMap = userAllList.stream().collect(Collectors.toMap(DtoUser::getId, DtoUser::getUserName));
        pageData.forEach(p -> {
            p.setCreatorName(userMap.get(p.getCreator()));
        });
    }

    @Override
    public String download(String logId, HttpServletResponse response) {
        DtoParseLog dtoLog = repository.findOne(logId);
        if (StringUtil.isNull(dtoLog)) {
            throw new BaseException("解析日期不存在!");
        }
        DtoParseFileApp fileApp = parseFileAppRepository.findOne(dtoLog.getAppId());
        if (StringUtil.isNull(fileApp)) {
            throw new BaseException("解析应用不存在!");
        }
        String str = "2".equals(dtoLog.getParseStatus()) ? "success" : "failure";
        String folderName = fileApp.getFolderName();
        String path = filePath + "/" + str + folderName.substring(folderName.indexOf("/")) + "/" + dtoLog.getFileName();
        log.info("开始下载解析日志文件，path = " + path);
        File file = new File(path);
        if (!file.exists()) {
            log.error("文件不存在：" + path);
            throw new BaseException("文件不存在,请确认 " + path);
        }
        String fileName = path.substring(path.lastIndexOf("/") + 1);

        downFile(file, response, fileName);
        return fileName;
    }

    /**
     * 文件下载
     *
     * @param file     文件对象
     * @param fileName 文件名
     * @param response 方案id
     */
    private void downFile(File file, HttpServletResponse response, String fileName) {
        FileInputStream fileInputStream = null;
        OutputStream outputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(fileName, "UTF-8"));
            outputStream = response.getOutputStream();
            byte[] bytes = new byte[2048];
            int len;
            while ((len = fileInputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("下载文件出错!");
        } finally {
            FileUtil.close(fileInputStream);
            FileUtil.close(outputStream);
        }
    }
}