package com.sinoyd.lims.pro.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.sinoyd.base.service.MessageReceiveMonitorService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.commons.enums.EnumDataOperation;
import com.sinoyd.commons.vo.MqDataVO;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.frame.service.IBaseJpaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 数据同步组件基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/24
 **/
@Service
@Slf4j
@SuppressWarnings("unchecked")
@DependsOn("exchangeName")
public abstract class AbsSyncComponent<T, ID extends Serializable, S extends IBaseJpaService<T, ID>, R extends JpaRepository<T, ID>> {

    @Autowired(required = false)
    protected S service;

    @Autowired(required = false)
    protected R repository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MessageReceiveMonitorService messageReceiveMonitorService;

    /**
     * 重试计数的Key名称（用于JSON消息体中）
     */
    private static final String RETRY_COUNT_KEY = "_retryCount";

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 1;

    /**
     * 获取当前泛型类型的简单名称。
     *
     * @return 返回当前泛型类型的简单名称。
     */
    public String getTClassName() {
        return getTClass().getSimpleName();
    }

    /**
     * 获取简单类名
     *
     * @return 返回简单类名
     */
    public String getSClassName() {
        return getSClass().getSimpleName();
    }

    /**
     * 处理消息的方法。
     *
     * @param msg         接收到的消息对象
     * @param deliveryTag 消息的投递标签
     * @param channel     RabbitMQ的通道对象
     * @throws IOException 如果在处理消息时发生IO异常
     */
    protected void processMsg(Message msg, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("queueName: {}", msg.getMessageProperties().getConsumerQueue());

        // 获取消息内容
        String messageContent = new String(msg.getBody());
        log.info("接收到的原始消息: {}", messageContent);

        // 解析消息内容为JSON，提取重试计数
        JSONObject jsonMessage;
        int retryCount = 0;

        try {
            jsonMessage = JSON.parseObject(messageContent);

            // 检查消息中是否包含重试计数
            if (jsonMessage.containsKey(RETRY_COUNT_KEY)) {
                retryCount = jsonMessage.getIntValue(RETRY_COUNT_KEY);
                log.info("从消息中读取到重试次数: {}", retryCount);
            }
        } catch (Exception e) {
            log.warn("消息格式不是JSON或解析重试计数失败，假设为首次处理", e);
            jsonMessage = new JSONObject();
            jsonMessage.put("originalMessage", messageContent);
        }

        try {
            // 处理业务逻辑
            if (StringUtil.isNotEmpty(messageContent)) {
                // 将原始消息内容(不含重试计数)传递给业务处理方法
                String businessMessage = getBusinessMessage(jsonMessage);
                log.info("业务处理的消息内容: {}", businessMessage);

                MqDataVO mqDataVO = parseMessage(businessMessage);
                log.info("解析后的消息: {}", mqDataVO);

                if (EnumDataOperation.新增.getValue().equals(mqDataVO.getOperation())) {
                    if (mqDataVO.getIsBatch()) {
                        save((List<T>) mqDataVO.getData(), mqDataVO.getIsRepository());
                    } else {
                        save((T) mqDataVO.getData(), mqDataVO.getIsRepository());
                    }
                } else if (EnumDataOperation.修改.getValue().equals(mqDataVO.getOperation())) {
                    if (mqDataVO.getIsBatch()) {
                        update((List<T>) mqDataVO.getData(), mqDataVO.getIsRepository());
                    } else {
                        update((T) mqDataVO.getData(), mqDataVO.getIsRepository());
                    }
                } else if (EnumDataOperation.删除.getValue().equals(mqDataVO.getOperation())) {
                    if (mqDataVO.getIsBatch()) {
                        delete((Collection<?>) mqDataVO.getData(), mqDataVO.getIsRepository());
                    } else {
                        delete((ID) mqDataVO.getData(), mqDataVO.getIsRepository());
                    }
                }
            }

            // 消息处理成功，手动确认
            channel.basicAck(deliveryTag, false);
            log.info("消息处理成功，已确认消息");

        } catch (Exception e) {
            log.error("处理消息失败: {}", e.getMessage(), e);

            log.warn("消息处理失败，当前重试次数: {}", retryCount);

            // 判断是否达到最大重试次数
            if (retryCount >= MAX_RETRY_COUNT) {
                log.warn("消息已达到最大重试次数 {}, 将消息存入数据库并确认消息", MAX_RETRY_COUNT);

                // 将失败消息保存到数据库
                //持久化service中增加一个方法接收下面的交换机、路由键、队列、消息ID等信息，然后根据这些信息进行重发
                MessageProperties properties = msg.getMessageProperties();
                log.info("将失败消息存入数据库");
                messageReceiveMonitorService.createAndSave(getBusinessMessage(jsonMessage),
                        properties.getReceivedExchange(), properties.getReceivedRoutingKey(),
                        properties.getConsumerQueue(), "消息消费失败");
                // 确认消息已处理，防止消息继续重试
                channel.basicAck(deliveryTag, false);
                log.info("消息已确认");
            } else {
                // 未达到最大重试次数，增加重试计数并重新发送消息
                retryCount++;
                log.info("消息处理失败，准备进行第 {} 次重试", retryCount);

                // 创建新的消息体，包含重试计数
                // 1. 如果原消息是JSON格式，在其中添加重试计数字段
                // 2. 如果原消息不是JSON格式，将其封装为JSON，添加重试计数字段
                try {
                    JSONObject newJsonMessage = jsonMessage != null ? jsonMessage : new JSONObject();
                    newJsonMessage.put(RETRY_COUNT_KEY, retryCount);

                    // 确保保留了原始业务数据
                    String originalBusinessMessage = getBusinessMessage(jsonMessage);
                    if (!newJsonMessage.containsKey("originalMessage") && !newJsonMessage.containsKey("operation")) {
                        newJsonMessage.put("originalMessage", originalBusinessMessage);
                    }

                    String newMessageContent = newJsonMessage.toJSONString();
                    log.info("重试消息内容: {}", newMessageContent);

                    // 创建新消息并发送
                    MessageProperties properties = msg.getMessageProperties();
                    Message newMsg = new Message(newMessageContent.getBytes(), properties);

                    // 确认并移除当前消息
                    channel.basicAck(deliveryTag, false);

                    // 重新发送到原队列
                    rabbitTemplate.send(properties.getReceivedExchange(),
                            properties.getReceivedRoutingKey(),
                            newMsg);

                    log.info("新消息已发送，等待重试，当前重试计数: {}", retryCount);
                } catch (Exception ex) {
                    log.error("创建重试消息失败: {}", ex.getMessage(), ex);
                    // 拒绝当前消息并重新入队
                    channel.basicReject(deliveryTag, true);
                    log.info("创建重试消息失败，拒绝当前消息并重新入队");
                }
            }
        }
    }

    /**
     * 从JSON消息中提取业务消息内容
     * 如果消息包含重试计数字段和原始消息字段，则返回原始消息
     * 否则返回去除重试计数字段后的JSON字符串
     *
     * @param jsonMessage JSON格式的消息
     * @return 业务处理所需的消息内容
     */
    private String getBusinessMessage(JSONObject jsonMessage) {
        if (jsonMessage == null) {
            return "";
        }

        // 如果有原始消息字段，直接返回
        if (jsonMessage.containsKey("originalMessage")) {
            Object original = jsonMessage.get("originalMessage");
            if (original instanceof String) {
                return (String) original;
            } else {
                return JSON.toJSONString(original);
            }
        }

        // 否则，克隆一份并移除重试计数字段
        JSONObject business = (JSONObject) jsonMessage.clone();
        business.remove(RETRY_COUNT_KEY);
        return business.toJSONString();
    }

    /**
     * 保存实体对象
     *
     * @param t            要保存的实体对象
     * @param isRepository 是否使用repository进行保存
     */
    protected void save(T t, boolean isRepository) {
        if (isRepository) {
            repository.save(t);
        } else {
            service.save(t);
        }
    }

    /**
     * 保存集合中的对象到数据库。
     *
     * @param ts           需要保存的对象集合
     * @param isRepository 是否使用repository进行保存
     */
    protected void save(Collection<T> ts, boolean isRepository) {
        if (isRepository) {
            repository.save(ts);
        } else {
            service.save(ts);
        }
    }

    /**
     * 更新实体对象
     *
     * @param t            要更新的实体对象
     * @param isRepository 是否使用re
     */
    protected void update(T t, boolean isRepository) {
        if (isRepository) {
            repository.save(t);
        } else {
            service.update(t);
        }
    }

    /**
     * 更新集合中的对象到数据库。
     *
     * @param ts           需要更新的对象集合
     * @param isRepository 是否使用repository进行更新
     */
    protected void update(Collection<T> ts, boolean isRepository) {
        if (isRepository) {
            repository.save(ts);
        } else {
            service.update(ts);
        }
    }

    /**
     * 逻辑删除指定ID集合的实体对象
     *
     * @param ids          要删除的实体对象的ID集合
     * @param isRepository 是否使用repository进行逻辑删除
     */
    protected void delete(Collection<?> ids, boolean isRepository) {
        if (isRepository) {
            if (repository instanceof IBaseJpaRepository) {
                ((IBaseJpaRepository<T, ID>) repository).logicDeleteById(ids, new Date());
            } else {
                ((IBaseJpaPhysicalDeleteRepository<T, ID>) repository).logicDeleteById(ids);
            }
        } else {
            service.logicDeleteById(ids);
        }
    }

    /**
     * 根据单个ID删除实体对象
     *
     * @param id           要删除的实体对象的ID
     * @param isRepository 是否使用re
     */
    protected void delete(ID id, boolean isRepository) {
        if (isRepository) {
            if (repository instanceof IBaseJpaRepository) {
                ((IBaseJpaRepository<T, ID>) repository).logicDeleteById(id, new Date());
            } else {
                ((IBaseJpaPhysicalDeleteRepository<T, ID>) repository).logicDeleteById(id);
            }
        } else {
            service.logicDeleteById(id);
        }
    }

    /**
     * 获取当前类的泛型类型参数T的Class对象。
     *
     * @return 返回泛型类型参数T的Class对象。
     */
    private Class<T> getTClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * 获取当前类的泛型类型参数S的Class对象。
     *
     * @return 返回泛型类型参数S的Class对象。
     */
    private Class<T> getSClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[2];
    }

    /**
     * 解析消息，并转换为 MqDataVO 对象
     *
     * @param message 接收到的消息字符串
     * @return 解析后的 MqDataVO 对象
     */
    private MqDataVO parseMessage(String message) {
        JSONObject jsonObject = JSON.parseObject(message);
        Integer operation = jsonObject.getInteger("operation");
        String dataStr = jsonObject.getString("data");
        String className = jsonObject.getString("clazzName");
        Boolean isBatch = jsonObject.getBoolean("isBatch");
        Boolean isRepository = jsonObject.getBoolean("isRepository");
        MqDataVO mqDataVO = MqDataVO.builder()
                .operation(operation)
                .isBatch(isBatch)
                .isRepository(isRepository)
                .clazzName(className)
                .build();
        if (EnumDataOperation.新增.getValue().equals(operation) || EnumDataOperation.修改.getValue().equals(operation)) {
            if (isBatch) {
                mqDataVO.setData(JSON.parseArray(dataStr, getTClass()));
            } else {
                mqDataVO.setData(JSON.parseObject(dataStr, getTClass()));
            }
        } else if (EnumDataOperation.删除.getValue().equals(operation)) {
            if (isBatch) {
                mqDataVO.setData(JSON.parseArray(dataStr, String.class));
            } else {
                mqDataVO.setData(dataStr);
            }
        }
        return mqDataVO;
    }
}
