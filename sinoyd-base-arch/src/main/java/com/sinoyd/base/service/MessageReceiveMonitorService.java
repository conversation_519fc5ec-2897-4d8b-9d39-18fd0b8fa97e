package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoMessageReceiveMonitor;
import com.sinoyd.frame.service.IBaseJpaService;

/**
 * 消费端消息监控服务接口
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/16
 * @since V100R001
 */
public interface MessageReceiveMonitorService extends IBaseJpaService<DtoMessageReceiveMonitor, String> {


    /**
     * 重发消息
     *
     * @param id 消息监控ID
     */
    void reSendMessage(String id);

    /**
     * 创建保存
     *
     * @param message    消息队列内容
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param queue      队列
     * @param reason     原因
     */
    void createAndSave(String message, String exchange, String routingKey, String queue, String reason);
}
