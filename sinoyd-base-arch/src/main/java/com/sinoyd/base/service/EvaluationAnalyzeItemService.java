package com.sinoyd.base.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationAnalyzeItem;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;


/**
 * EvaluationAnalyzeItem操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationAnalyzeItemService extends IBaseJpaService<DtoEvaluationAnalyzeItem, String> {

    /**
     * 批量保存分析项目(业务逻辑处理)
     *
     * @param dtoEvaluationAnalyzeItems 评价分析项目集合
     * @return 保存后的分析项目集合
     */
    List<DtoEvaluationAnalyzeItem> saveBatch(Collection<DtoEvaluationAnalyzeItem> dtoEvaluationAnalyzeItems);

    /**
     * 根据评价等级id获取相应的评价分析项目
     *
     * @param evaluationId 评价等级id
     * @return 返回相应的评价信息
     */
    List<DtoEvaluationAnalyzeItem> findAnalyzeItemByEvaluationId(String evaluationId);

    /**
     * 根据评价标准id及要删除的分析项目ids
     *
     * @param evaluationId         评价标准ID
     * @param deleteAnalyzeItemIds 带删除的分析项目ids
     * @return 返回删除的行数
     */
    Integer delete(String evaluationId, List<String> deleteAnalyzeItemIds);
}