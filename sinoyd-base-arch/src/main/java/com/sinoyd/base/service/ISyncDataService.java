package com.sinoyd.base.service;

import com.sinoyd.frame.base.entity.BaseEntity;

import java.util.List;

/**
 * 同步数据通用接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/14
 */
public interface ISyncDataService<T extends BaseEntity> {

    /**
     * 同步数据
     *
     * @param sourceDataList 待同步数据集合
     * @param limsDeleteDataList  lims中假删的数据集合
     */
    void sync(List<T> sourceDataList, List<T> limsDeleteDataList);
}
