package com.sinoyd.base.service;

import com.sinoyd.base.dto.customer.DtoExcelParam;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.service.IBaseJpaService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件接口
 *
 * <AUTHOR>
 * @version v1.0.0 2018/12/6
 * @since V100R001
 */
public interface DocumentService extends IBaseJpaService<DtoDocument, String> {


    /**
     * 以流的形式下载附件，如果根据
     *
     *
     * @param document 附件对象
     * @return  Map<文件名, 文件字节数组>
     */
    Map<String, byte[]> downloadAsStream(DtoDocument document);

    /**
     * 下载文件
     *
     * @param documentId 文件id
     * @param response   响应流
     * @return 下载文件路径
     */
    String download(String documentId, HttpServletResponse response) throws IOException;

    /**
     * 下载文件(返回文件绝对路径)
     *
     * @param documentId 文件id
     * @return 下载文件的绝对路径
     */
    String downloadPath(String documentId);

    /**
     * 根据对象id
     *
     * @param objectId 对象id
     * @param path     返回的文件路径
     * @param response 响应流
     * @return 下载文件路径
     */
    String download(String objectId, String path, HttpServletResponse response) throws IOException;

    /**
     * 根据上传路径下载报表模板
     *
     * @param document 文档
     * @param response 响应体
     * @return 下载文件路径
     */
    String downloadReport(DtoDocument document, HttpServletResponse response) throws IOException;

    /**
     * 根据对象id
     *
     * @param documentId  文件id
     * @param excelParams excel参数实体
     * @return
     */
    String syncExcel(String documentId, List<DtoExcelParam> excelParams);

    /**
     * 文件上传（默认返回一个路径）
     *
     * @param request 文件上传对象
     */
    List<DtoDocument> upload(HttpServletRequest request, List<String> allowSuffixList);

    /**
     * 报表模板配置文件上传
     *
     * @param request 文件上传对象
     */
    List<DtoDocument> uploadReportConfig(HttpServletRequest request);


    /**
     * 得到文件上传的路径
     *
     * @param code 通过编码锁定相应的反射类
     * @param map  前端传相应的参数集合
     * @return 返回文件上传的路径
     */
    String getDocumentPath(String code, Map<String, Object> map) throws Exception;

    /**
     * 得到文件上传的路径
     *
     * @param code 编码
     * @param map  format替换的map
     * @return 返回文件上传的路径
     */
    String getDocumentPathByPlaceholder(String code, Map<String, Object> map);

    /**
     * 图片路径转成字符串（按大小压缩）
     *
     * @param url 路径
     * @return 返回相应的字符串
     */
    String convertBase64Content(String url, Integer width, Integer height);


    /**
     * 图片路径转成字符串（不压缩）
     *
     * @param url 路径
     * @return 返回相应的字符串
     */
    String convertBase64Content(String url);

    /**
     * 将文件转成base64字符串
     *
     * @param url  文件路径
     * @return  字符串
     */
    String convertFileToBase64(String url);

    /**
     * 上次base 64的文件
     *
     * @param request 请求方式
     * @return 返回数据
     */
    Boolean uploadBase64Content(HttpServletRequest request);

    /**
     * 删除文档数据
     *
     * @param folderId 文件夹id
     * @return 返回删除行数
     */
    Integer deleteByFolderId(String folderId);

    /**
     * 将质控申请下的附件,拷贝一份到文件质控明细下
     *
     * @param fileId  文件质控明细id
     * @param foldId  质控申请id
     * @param newPath 路径
     */
    void syncFileControlApplyDetail(String fileId, String foldId, String newPath);


    /**
     * 根据对象获取数据
     *
     * @param objectId 对象id
     * @return 返回数据
     */
    List<DtoDocument> findByObjectId(String objectId);

    /**
     * 根据对象获取数据
     *
     * @param objectIds 对象id集合
     * @return 返回数据
     */
    List<DtoDocument> findByObjectIds(Collection<String> objectIds);

    /**
     * 打包指定目录压缩为zip文件，压缩包直接放在response流中返回前端
     *
     * @param directory 要压缩的路径
     * @param response  response流
     * @return 结果标示
     */
    String directoryToZip(String directory, HttpServletResponse response);

    /**
     * 下载的公共方法封装
     *
     * @param path     下载路径
     * @param filename 文件名称
     * @param response 浏览器响应数据
     * @return 返回数据信息
     */
    String fileDownload(String path, String filename, HttpServletResponse response) throws IOException;

    /**
     * 文件预览
     *
     * @param code     路径类型
     * @param vo       文件预览参数传输对象
     * @param response 响应流
     */
    void preview(String code, DocumentPreviewVO vo, HttpServletResponse response);

    /**
     * 复制文件
     * @param code         编码
     * @param folderId     对象id
     * @param documentId   源文件id
     */
    void copyFile(String code, String folderId, String documentId);

    /**
     * 重命名文件
     *
     * @param documentId  文件id
     * @param fileName    文件名
     */
    void renameFile(String documentId, String fileName);

    /**
     * 测点示意图在线编辑
     *
     * @param request 请求
     */
    void onlineEdit(HttpServletRequest request);

    /**
     * 文件转换blob
     *
     * @param path 路径
     * @return byte[]
     */
    byte[] convertFile2Blob(String path);

    /**
     * 获取允许上传文件后缀
     * @return String
     */
    String getAllowFileSuffix();

    /**
     * 多文件压缩下载
     * @param documentIds 文件标识集合
     * @param response 响应流
     */
    void batchZipDownload(List<String> documentIds,HttpServletResponse response);
}