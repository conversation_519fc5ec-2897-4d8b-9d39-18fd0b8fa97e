package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;
import java.util.Map;

/**
 * 仪器管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface InstrumentService extends IBaseJpaService<DtoInstrument, String> {
    /**
     * 复制仪器
     *
     * @param instrumentId    被复制的仪器id
     * @param instrumentsCode 仪器编号
     * @param serialNo        出厂编号
     */
    DtoInstrument copy(String instrumentId, String instrumentsCode, String serialNo);

    /***
     * 获取制造厂商
     * @return
     */
    List<DtoInstrument> getFactoryNameList();

    /***
     * 获取溯源单位
     * @return
     */
    List<DtoInstrument> getOriginUnitList();


    /**
     * 找到仪器归档附件
     *
     * @param id             仪器id
     * @param instrumentName 仪器名称
     * @return 返回归档信息
     */
    DtoInstrument findInstrumentAttachment(String id, String instrumentName);


    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 主键ids
     * @return 返回带有假删的数据
     */
    List<DtoInstrument> findAllDeleted(List<String> ids);


    /**
     * 含假删的信息
     *
     * @return 返回假删信息
     */
    List<DtoInstrument> findAllDeleted();

    /**
     * 根据项目id,送样单采样人过滤已出库的仪器id
     *
     * @param receiveId 送样单id
     * @return 仪器id列表
     */
    List<String> filterInstrumentId(String receiveId);

    /**
     * 根据项目id,送样单采样人过滤出仪器出入库记录id
     *
     * @param receiveId 送样单id
     * @return 仪器出入库记录id列表
     */
    Map<String, Object> filterProjectInstrumentId(String receiveId);

    /***
     *  仪器过期明细
     *
     * @return 仪器过期明细
     */
    List<DtoInstrument> getOverDueData();

    /***
     *  仪器即将过期明细
     *
     * @return 仪器过期明细
     */
    List<DtoInstrument> getWillOverDueData();

    /**
     * 获取当前最大的排序值
     *
     * @return 最大的排序值
     */
    Integer getLastOrderNum();
}