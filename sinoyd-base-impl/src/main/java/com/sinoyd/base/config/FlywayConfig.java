package com.sinoyd.base.config;

import com.sinoyd.boot.workflow.activiti.constant.BeanNameConstant;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Flyway配置类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/17
 */
@Configuration
@EnableTransactionManagement
public class FlywayConfig {

    /**
     * 框架相关sql文件位置
     */
    @Value("${flyway.path.frame:NONE}")
    private String FRAME_SQL_LOCATION;

    /**
     * lims sql文件位置
     */
    @Value("${flyway.path.lims:NONE}")
    private String LIMS_SQL_LOCATION;

    /**
     * rcc sql文件位置
     */
    @Value("${flyway.path.rcc:NONE}")
    private String RCC_SQL_LOCATION;

    /**
     * 工作流 初始化数据 sql文件位置
     */
    @Value("${flyway.path.act:NONE}")
    private String ACT_SQL_LOCATION;

    @Value("${spring.datasource.rcc.username:unknown}")
    private String rccDbUserName;

    @Value("${spring.datasource.rcc.password:unknown}")
    private String rccDbUserPassword;

    @Value("${lims.rcc.enabled:false}")
    private Boolean enabledRcc;

    /**
     * flyway使用的编码
     */
    private static final String ENCODING = "UTF-8";

    @Autowired
    private Map<String, DataSource> dataSourceMap;


    private void migrateFrame() {
        DataSource frameDs = dataSourceMap.get("frameDataSource");
        if (frameDs != null && !"NONE".equalsIgnoreCase(FRAME_SQL_LOCATION)) {
            Flyway flyway = Flyway.configure()
                    .dataSource(frameDs)
                    .encoding(ENCODING)
                    .baselineOnMigrate(true)
                    .locations(FRAME_SQL_LOCATION)
                    .validateOnMigrate(false)
                    .load();
            flyway.migrate();
        }
    }

    private void migrateLims() {
        DataSource limsDs = dataSourceMap.get("primaryDataSource");
        if (limsDs != null && !"NONE".equalsIgnoreCase(LIMS_SQL_LOCATION)) {
            List<String> sqlLocations = new ArrayList<>();
            sqlLocations.add(LIMS_SQL_LOCATION);
            Flyway flyway = Flyway.configure()
                    .dataSource(limsDs)
                    .encoding(ENCODING)
                    .baselineOnMigrate(true)
                    .locations(sqlLocations.toArray(new String[0]))
                    .validateOnMigrate(false)
                    .placeholderPrefix("${")
                    .placeholderSuffix("}")
                    .placeholderReplacement(true)
                    .load();
            flyway.migrate();
        }
    }

    private void migrateAct() {
        DataSource actDs = dataSourceMap.get(BeanNameConstant.DATASOURCE_ACTIVITI);
        if (actDs != null && !"NONE".equalsIgnoreCase(ACT_SQL_LOCATION)) {
            Flyway flyway = Flyway.configure()
                    .dataSource(actDs)
                    .encoding(ENCODING)
                    .baselineOnMigrate(true)
                    .locations(ACT_SQL_LOCATION)
                    .validateOnMigrate(false)
                    .load();
            flyway.migrate();
        }
    }

    @PostConstruct
    public void migrate() throws SQLException {
        migrateFrame();
        migrateLims();
    }

}