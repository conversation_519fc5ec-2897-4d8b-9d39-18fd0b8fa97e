package com.sinoyd.base.utils.poi;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

/**
 * POI Excel处理工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2022-03-08
 * @since V1.0.0
 */
@Slf4j
public class PoiExcelUtils {

    /**
     * 得到Workbook对象
     *
     * @param file Excel文件
     * @return Workbook对象
     * @throws IOException 异常
     */
    public static Workbook getWorkBook(MultipartFile file) throws IOException {
        //这样写兼容03和07
        InputStream is = file.getInputStream();
        Workbook workbook;
        try {
            workbook = new HSSFWorkbook(is);
        } catch (Exception ex) {
            is = file.getInputStream();
            workbook = new XSSFWorkbook(is);
        } finally {
            is.close();
        }
        return workbook;
    }


    /**
     * excel 导出
     *
     * @param list           数据
     * @param title          标题
     * @param sheetName      sheet名称
     * @param pojoClass      pojo类型
     * @param fileName       文件名称
     * @param isCreateHeader 是否创建表头
     * @param response       响应流
     */
    public static void exportExcel(List<?> list,
                                   String title,
                                   String sheetName,
                                   Class<?> pojoClass,
                                   String fileName,
                                   boolean isCreateHeader,
                                   HttpServletResponse response) {
        ExportParams exportParams = new ExportParams(title, sheetName, ExcelType.XSSF);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * excel 导出
     *
     * @param list      数据
     * @param title     标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName  文件名称
     * @param response  响应流
     */
    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, HttpServletResponse response) {
        defaultExport(list, pojoClass, fileName, response, new ExportParams(title, sheetName, ExcelType.XSSF));
    }

    /**
     * excel 导出
     *
     * @param list         数据
     * @param pojoClass    pojo类型
     * @param fileName     文件名称
     * @param response     响应流
     * @param exportParams 导出参数
     */
    public static void exportExcel(List<?> list, Class<?> pojoClass, String fileName, ExportParams exportParams, HttpServletResponse response) {
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * excel 导出
     *
     * @param list     数据
     * @param fileName 文件名称
     * @param response 响应流
     */
    public static void exportExcel(List<Map<String, Object>> list, String fileName, HttpServletResponse response) {
        defaultExport(list, fileName, response);
    }

    /**
     * 默认的 excel 导出
     *
     * @param list         数据
     * @param pojoClass    pojo类型
     * @param fileName     文件名称
     * @param response     响应流
     * @param exportParams 导出参数
     */
    private static void defaultExport(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, ExportParams exportParams) {
        exportParams.setStyle(ExcelStyle.class);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        downLoadExcel(fileName, response, workbook);
    }

    /**
     * 默认的 excel 导出
     *
     * @param list     数据
     * @param fileName 文件名称
     * @param response 响应流
     */
    private static void defaultExport(List<Map<String, Object>> list, String fileName, HttpServletResponse response) {
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        downLoadExcel(fileName, response, workbook);
    }

    private static XSSFWorkbook convertWorkbookHSSF(Workbook workbook) {
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(sheet.getSheetName());
            copySheetHSSF(sheet, xssfSheet, xssfWorkbook);
        }
        return xssfWorkbook;
    }

    private static void copySheetHSSF(Sheet srcSheet, Sheet destSheet, Workbook workbook) {
        //合并单元格
        int numMergedRegions = srcSheet.getNumMergedRegions();
        for (int i = 0; i < numMergedRegions; i++) {
            CellRangeAddress mergedRegion = srcSheet.getMergedRegion(i);
            destSheet.addMergedRegion(mergedRegion);
        }
        //增加列宽
        int physicalNumberOfCells = srcSheet.getRow(0).getPhysicalNumberOfCells();
        for (int i = 0; i < physicalNumberOfCells; i++) {
            destSheet.setColumnWidth(i, 256 * 20);
        }
        //最大获取行数
        int maxRowSize = srcSheet.getPhysicalNumberOfRows();
        for (int i = 0; i < maxRowSize; i++) {
            Row newRow = destSheet.createRow(i);
            Row oldRow = srcSheet.getRow(i);
            //此处需要将 cellStyle 定义在遍历行的地方,定义在外面可能出现样式渲染错误
            CellStyle cellStyle = workbook.createCellStyle();
            //水平居中/垂直居中
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setWrapText(true);
            //下边框
            cellStyle.setBorderBottom(BorderStyle.THIN);
            //左边框
            cellStyle.setBorderLeft(BorderStyle.THIN);
            //上边框
            cellStyle.setBorderTop(BorderStyle.THIN);
            //右边框
            cellStyle.setBorderRight(BorderStyle.THIN);
            //获取当前行,最大列数
            int maxColSize = oldRow.getLastCellNum();
            for (int j = 0; j < maxColSize; j++) {
                Cell newCell = newRow.createCell(j);
                Cell oldCell = oldRow.getCell(j);
                if (oldCell == null) {
                    continue;
                }
                copyCell(oldCell, newCell);
                newCell.setCellStyle(cellStyle);
            }
        }
    }

    private static void copyCell(Cell srcCell, Cell destCell) {
        destCell.setCellType(srcCell.getCellType());
        switch (srcCell.getCellType()) {
            case STRING:
                destCell.setCellValue(srcCell.getStringCellValue());
                break;
            case NUMERIC:
                destCell.setCellValue(srcCell.getNumericCellValue());
                break;
            case BOOLEAN:
                destCell.setCellValue(srcCell.getBooleanCellValue());
                break;
            case FORMULA:
                destCell.setCellFormula(srcCell.getCellFormula());
                break;
            default: // 处理其他类型如 BLANK, ERROR 等，根据需要添加代码处理这些类型
                break;
        }
    }

    /**
     * 下载
     *
     * @param fileName 文件名称
     * @param response 响应流
     * @param workbook excel数据
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + "." + ExcelTypeEnum.XLSX.getValue(), "UTF-8"));
            convertWorkbookHSSF(workbook).write(response.getOutputStream());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("下载文件发生错误");
        }
    }

    /**
     * excel 导入
     *
     * @param filePath   excel文件路径
     * @param titleRows  标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>        实体类型
     * @return 数据集合
     */
    public static <T> List<T> importExcel(String filePath, Integer titleRows, Integer headerRows, Class<T> pojoClass) {
        if (StringUtil.isEmpty(filePath)) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setNeedSave(true);
        params.setSaveUrl("/excel/");
        try {
            return ExcelImportUtil.importExcel(new File(filePath), pojoClass, params);
        } catch (NoSuchElementException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("模板不能为空");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("导入数据发生未知错误");
        }
    }

    /**
     * excel 导入
     *
     * @param file      excel文件
     * @param pojoClass pojo类型
     * @param <T>       实体类型
     * @return 数据列表
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> pojoClass) throws IOException {
        return importExcel(file, 1, 1, pojoClass);
    }

    /**
     * excel 导入
     *
     * @param file       excel文件
     * @param titleRows  标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>        实体类型
     * @return 数据列表
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, Class<T> pojoClass) {
        return importExcel(file, titleRows, headerRows, false, pojoClass);
    }

    /**
     * excel 导入
     *
     * @param file       上传的文件
     * @param titleRows  标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>        实体类型
     * @return 数据集合
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, boolean needVerify, Class<T> pojoClass) {
        if (file == null) {
            return null;
        }
        try {
            return importExcel(file.getInputStream(), titleRows, headerRows, needVerify, pojoClass);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("导入数据发生未知错误");
        }
    }

    /**
     * excel 导入
     *
     * @param inputStream 文件输入流
     * @param titleRows   标题行
     * @param headerRows  表头行
     * @param needVerify  是否检验excel内容
     * @param pojoClass   pojo类型
     * @param <T>         实体类型
     * @return 数据列表
     */
    public static <T> List<T> importExcel(InputStream inputStream, Integer titleRows, Integer headerRows, boolean needVerify, Class<T> pojoClass) {
        if (inputStream == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setSaveUrl("/excel/");
        params.setNeedSave(true);
        try {
            return ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (NoSuchElementException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("Excel文件不能为空");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("导入数据发生未知错误");
        }
    }

    /**
     * 校验文件扩展名
     *
     * @param file 文件
     */
    public static void verifyFileType(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!ExcelTypeEnum.XLSX.getValue().equals(fileExtension) && !ExcelTypeEnum.XLS.getValue().equals(fileExtension)) {
            throw new BaseException("只能导入文件扩展名为 xls 或 xlsx 类型的表格文件");
        }
    }

    /**
     * 复制sheet
     *
     * @param newSheet
     * @param oldSheet
     * @return
     */
    public static Sheet copySheet(Sheet newSheet, Sheet oldSheet, Workbook workbook) {

        //合并单元格
        int numMergedRegions = oldSheet.getNumMergedRegions();
        for (int i = 0; i < numMergedRegions; i++) {
            CellRangeAddress mergedRegion = oldSheet.getMergedRegion(i);
            newSheet.addMergedRegion(mergedRegion);
        }
        //增加列宽
        int physicalNumberOfCells = oldSheet.getRow(0).getPhysicalNumberOfCells();
        for (int i = 0; i < physicalNumberOfCells; i++) {
            newSheet.setColumnWidth(i, 256 * 20);
        }

        //最大获取行数
        int maxRowSize = oldSheet.getPhysicalNumberOfRows();
        for (int i = 0; i < maxRowSize; i++) {
            Row newRow = newSheet.createRow(i);
            Row oldRow = oldSheet.getRow(i);
            //此处需要将 cellStyle 定义在遍历行的地方,定义在外面可能出现样式渲染错误
            CellStyle cellStyle = workbook.createCellStyle();
            //水平居中/垂直居中
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setWrapText(true);
            //下边框
            cellStyle.setBorderBottom(BorderStyle.THIN);
            //左边框
            cellStyle.setBorderLeft(BorderStyle.THIN);
            //上边框
            cellStyle.setBorderTop(BorderStyle.THIN);
            //右边框
            cellStyle.setBorderRight(BorderStyle.THIN);
            //定义字体,具体自己写
            Font font = workbook.createFont();
            //获取当前行,最大列数
            int maxColSize = oldRow.getLastCellNum();
            for (int j = 0; j < maxColSize; j++) {
                Cell newCell = newRow.createCell(j);
                Cell oldCell = oldRow.getCell(j);
                if (oldCell == null) {
                    continue;
                }
                CellType cellType = oldCell.getCellType();
                //此处type类型不只这两种,在此只是列举2种
                if ("NUMERIC".equals(cellType.name())) {
                    newCell.setCellValue(oldCell.getNumericCellValue());
                } else {
                    newCell.setCellValue(oldCell.getStringCellValue());
                }

                //1.直接copy原cell的样式,猜测:会导致大量创建cellStyle对象,引起excel问题
                //获取原cell的样式
                CellStyle oldCellStyle = oldCell.getCellStyle();
                cellStyle.cloneStyleFrom(oldCellStyle);

                newCell.setCellStyle(cellStyle);
            }
        }
        return newSheet;
    }

    /**
     * Excel 类型枚举
     */
    enum ExcelTypeEnum {
        XLS("xls"), XLSX("xlsx");
        private String value;

        ExcelTypeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}