package com.sinoyd.base.utils;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPoolConfig;

import java.net.URI;

/**
 * Redis连接实例工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/12
 */
@Slf4j
@Component
public class RedisTemplateUtils {

    /**
     * 创建基于连接字符串的Redis连接实例
     *
     * @param redisConnectStr Redis连接字符串，格式：redis://[password@]host:port/database
     * @return RedisTemplate实例
     */
    public RedisTemplate<String, Object> createRedisTemplateByConnectStr(String redisConnectStr) {
        try {
            // 参数校验
            if (StringUtil.isEmpty(redisConnectStr)) {
                throw new BaseException("Redis连接字符串不能为空");
            }

            // 解析Redis连接字符串
            URI redisUri = URI.create(redisConnectStr);
            String host = redisUri.getHost();
            int port = redisUri.getPort();
            String password = null;
            int database = 0;

            // 解析密码（如果存在）
            String userInfo = redisUri.getUserInfo();
            if (StringUtil.isNotEmpty(userInfo) && userInfo.contains(":")) {
                password = userInfo.split(":")[1];
            }

            // 解析数据库编号
            String path = redisUri.getPath();
            if (StringUtil.isNotEmpty(path) && path.length() > 1) {
                try {
                    database = Integer.parseInt(path.substring(1));
                } catch (NumberFormatException e) {
                    log.warn("解析Redis数据库编号失败，使用默认值0: {}", e.getMessage());
                }
            }

            // 调用自定义创建方法
            return createCustomRedisTemplate(host, port, password, database);

        } catch (Exception e) {
            log.error("基于连接字符串创建Redis连接实例失败: {}", e.getMessage(), e);
            throw new BaseException("基于连接字符串创建Redis连接实例失败: " + e.getMessage());
        }
    }

    /**
     * 创建自定义Redis连接实例
     *
     * @param host     Redis服务器地址
     * @param port     Redis服务器端口
     * @param password Redis密码，可为空
     * @param database Redis数据库编号
     * @return 自定义的RedisTemplate实例
     */
    public synchronized RedisTemplate<String, Object> createCustomRedisTemplate(String host, int port, String password, int database) {
        try {
            // 参数校验
            if (StringUtil.isEmpty(host)) {
                throw new BaseException("Redis服务器地址不能为空");
            }
            if (port <= 0 || port > 65535) {
                throw new BaseException("Redis服务器端口号无效");
            }
            if (database < 0) {
                throw new BaseException("Redis数据库编号不能为负数");
            }

            // 创建Jedis连接池配置
            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(8);
            poolConfig.setMaxIdle(8);
            poolConfig.setMinIdle(0);
            poolConfig.setMaxWaitMillis(-1);
            poolConfig.setTestOnBorrow(false);
            poolConfig.setTestOnReturn(false);
            poolConfig.setTestWhileIdle(true);

            // 创建Jedis连接工厂
            JedisConnectionFactory connectionFactory = new JedisConnectionFactory(poolConfig);
            connectionFactory.setHostName(host);
            connectionFactory.setPort(port);
            connectionFactory.setDatabase(database);
            connectionFactory.setTimeout(100000);

            if (StringUtil.isNotEmpty(password)) {
                connectionFactory.setPassword(password);
            }

            // 初始化连接工厂
            connectionFactory.afterPropertiesSet();

            // 创建RedisTemplate
            RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
            redisTemplate.setConnectionFactory(connectionFactory);

            // 设置序列化器
            StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
            GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer();

            // 设置key和hashKey的序列化器
            redisTemplate.setKeySerializer(stringRedisSerializer);
            redisTemplate.setHashKeySerializer(stringRedisSerializer);

            // 设置value和hashValue的序列化器
            redisTemplate.setValueSerializer(jsonRedisSerializer);
            redisTemplate.setHashValueSerializer(jsonRedisSerializer);

            // 初始化RedisTemplate
            redisTemplate.afterPropertiesSet();

            log.info("自定义Redis连接实例创建成功，连接地址: {}:{}, 数据库: {}", host, port, database);
            return redisTemplate;

        } catch (Exception e) {
            log.error("创建自定义Redis连接实例失败: {}", e.getMessage(), e);
            throw new BaseException("创建自定义Redis连接实例失败: " + e.getMessage());
        }
    }
}
