package com.sinoyd.base.utils.base;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.QcRulesConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.revise.ReviseDataFactory;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class DivationUtils {

    private static final Pattern PATTERN = Pattern.compile("\\[\\w+\\]");
    private static final Pattern MAP_PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    /**
     * 判断是否为数字
     *
     * @param value 字符串
     * @return 是或否
     */
    public static boolean isNumber(Object value) {
        if (StringUtil.isNotNull(value)) {
            if (value instanceof Number) {
                return true;
            } else if (value instanceof String) {
                return stringIsNumeral((String) value);
            }
        }
        return false;
    }

    /**
     * 质控限值范围通用计算方法
     *
     * @param range 范围
     * @return 是否通过
     */
    public static Boolean calculationResult(String range, BigDecimal data, CalculationService calculationService) {
        Boolean flag = true;
        if (StringUtil.isNotEmpty(range)) {
            Matcher m = PATTERN.matcher(range);
            while (m.find()) {
                String matchWord = m.group(0);
                Map<String, Object> map = new HashMap<>();
                map.put(matchWord.replace("[", "").replace("]", ""), data);
                // 修正之后的范围
                String rangeCorrect = UpdateOperators(range);
                log.info("...... DivationUtils.calculationResult: 公式 - {}, 参数 - {}", rangeCorrect, map);
                Object result = calculationService.calculationExpression(rangeCorrect, map);
                if (result instanceof Boolean) {
                    flag = (Boolean) result;
                } else if (result instanceof String) {
                    String flagResult = ((String) result);
                    if (flagResult.equals("false")) {
                        flag = false;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 进行操作运算符是修饰
     *
     * @param range 需要被修改的运算符
     * @return 修改之后的运算符
     */
    public static String UpdateOperators(String range) {
        String rangeCorrect = "";
        // 如果存在and这将and装换为" && ",并替换其他的操作字符
        if (range.contains(EnumBase.EnumSymbol.并且.getValue())) {
            String[] rangeArr = range.split(EnumBase.EnumSymbol.并且.getValue());
            return UpdateOperators(rangeArr[0]) + " && " + UpdateOperators(rangeArr[1]);
        }
        // 如果存在>=.将>=装换为" >= ";否则如果存在>,将>转为" > "
        if (range.contains(EnumBase.EnumSymbol.大于等于.getValue())) {
            rangeCorrect = range.replace(EnumBase.EnumSymbol.大于等于.getValue(), " >= ");
        } else if (range.contains(EnumBase.EnumSymbol.大于.getValue())) {
            rangeCorrect = range.replace(EnumBase.EnumSymbol.大于.getValue(), " > ");
        }

        // 如果存在<=.将<=装换为" <= ";否则如果存在<,将<转为" < "
        if (range.contains(EnumBase.EnumSymbol.小于等于.getValue())) {
            rangeCorrect = range.replace(EnumBase.EnumSymbol.小于等于.getValue(), " <= ");
        } else if (range.contains(EnumBase.EnumSymbol.小于.getValue())) {
            rangeCorrect = range.replace(EnumBase.EnumSymbol.小于.getValue(), " < ");
        }
        return rangeCorrect;
    }

    /**
     * 判断字符串是否是数字
     *
     * @param value 字符串
     */
    private static Boolean stringIsNumeral(String value) {
        if (value.contains("e") || value.contains("E")) {
            try {
                new BigDecimal(value).toPlainString();
            } catch (Exception e) {
                //转换失败，说明是非数字
                return false;
            }
            return true;
        } else if (StringUtils.isNotNullAndEmpty(value) && (!value.trim().equals(""))) {
            if (value.startsWith("-") || value.startsWith("+")) {
                if (value.length() == 1) {
                    return false;
                }
                value = value.substring(1);
            }

            // hex
            if (value.length() > 2 && (value.startsWith("0x") || value.startsWith("0X"))) {
                char[] charArr = value.substring(2).toCharArray();
                for (char c : charArr) {
                    if (!((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'))) {
                        return false;
                    }
                }
                return true;
            }

            // 0-9,Point,Scientific
            Integer p = 0, s = 0, l = value.length();
            char[] charArr = value.toCharArray();
            for (Integer i = 0; i < l; i++) {
                // Point
                if (charArr[i] == '.') {
                    if (p > 0 || s > 0 || i + 1 == l) {
                        return false;
                    }
                    p = i;
                } else if (charArr[i] == 'e' || charArr[i] == 'E') {
                    // Scientific
                    if (i == 0 || s > 0 || i + 1 == l) {
                        return false;
                    }
                    s = i;
                } else if (charArr[i] < '0' || charArr[i] > '9') {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 统计给定的子串subStr在主串str中出现的次数
     *
     * @param str    主串
     * @param subStr 子串
     * @return 子串subStr在主串str中出现的次数
     */
    public static int cntSubStr(String str, String subStr) {
        if (StringUtil.isEmpty(str) || StringUtil.isEmpty(subStr)) {
            return 0;
        }
        int idx = str.indexOf(subStr);
        return idx == -1 ? 0 : 1 + cntSubStr(str.substring(idx + subStr.length()), subStr);
    }

    /**
     * 开根号(牛顿大数法)
     *
     * @param value 数据
     * @param scale 小数位数
     * @return 开根号结果
     */
    public static BigDecimal sqrt(BigDecimal value, int scale) {
        BigDecimal num2 = BigDecimal.valueOf(2);
        int precision = 100;
        MathContext mc = new MathContext(precision, RoundingMode.HALF_UP);
        BigDecimal deviation = value;
        if (value.compareTo(BigDecimal.ZERO) != 0) {
            int cnt = 0;
            while (cnt < precision) {
                deviation = (deviation.add(value.divide(deviation, mc))).divide(num2, mc);
                cnt++;
            }
            deviation = deviation.setScale(scale, BigDecimal.ROUND_HALF_UP);
        }
        return deviation;
    }

    /**
     * 计算标准差S（bigDecimal）
     *
     * @param numberList 数据列表
     * @param number_1   均值
     * @return 标准差
     */
    public static BigDecimal standardDeviationBigDecimal(List<String> numberList, String number_1) {
        if (numberList == null || numberList.size() == 0) {
            throw new RuntimeException("标准差计算时接收的原始数据集合是空");
        }
        numberList.forEach(p -> {
            if (!isNumber(p)) {
                throw new RuntimeException("标准差计算时，原始数据集合中存在非数字");
            }
        });
        if (!isNumber(number_1)) {
            throw new RuntimeException("标准差计算平均值存在非数字或空值");
        }
        BigDecimal avg = new BigDecimal(number_1);
        List<BigDecimal> values = numberList.parallelStream().map(BigDecimal::new).collect(Collectors.toList());
        int size = values.size();
        BigDecimal s = BigDecimal.ZERO;
        //求方差
        for (BigDecimal value : values) {
            s = s.add((value.subtract(avg)).multiply(value.subtract(avg)));
        }
        s = s.divide(new BigDecimal(size - 1), 10, BigDecimal.ROUND_HALF_EVEN);
        //开根号
        return sqrt(s, 10);
    }

    /**
     * 置信系数计算
     *
     * @param count     个数
     * @param deviation 标准差值
     * @return 置信系数值
     */
    public static BigDecimal confidenceCoefficient(int count, String deviation) {
        String coefficient = BigDecimal.ZERO.toString();
        BigDecimal actionValue = new BigDecimal(deviation);
        //系数值(需要count大于5)
        if (count > BigDecimal.ROUND_HALF_DOWN) {
            coefficient = EnumBase.EnumCoefficientTable.EnumReValue(count - 1);
        }
        //置信系数 = 系数值*标准偏差/根号count
        return new BigDecimal(coefficient).multiply(actionValue).divide(DivationUtils
                .sqrt(new BigDecimal(count), BigDecimal.ROUND_HALF_EVEN), BigDecimal.ROUND_HALF_DOWN);
    }

    /**
     * 获取标准差修约规则
     *
     * @param codeService 常量服务对象
     * @return 修约规则
     */
    public static int[] getStdDevRoundingRule(CodeService codeService) {
        return getStdDevRoundingRuleByXml(SpringContextAware.getBean(QcRulesConfig.class));
    }

    /**
     * 获取标准差修约规则
     *
     * @param qcRulesConfig 常量服务对象
     * @return 修约规则
     */
    public static int[] getStdDevRoundingRuleByXml(QcRulesConfig qcRulesConfig) {
        List<OrderReviseVO> orderAndRevise = qcRulesConfig.getQualityReviseVo().getOrderAndRevise();
        int[] rule = new int[]{-1, -1};
        if (StringUtil.isNotNull(qcRulesConfig)) {
            OrderReviseVO bzVo = orderAndRevise.stream().filter(p -> "QualityParallel".equals(p.getCode())).findFirst().orElse(null);
            if (StringUtil.isNotNull(bzVo)) {
                rule[0] = StringUtil.isNotNull(bzVo.getSign()) ? bzVo.getSign() : -1;
                rule[1] = StringUtil.isNotNull(bzVo.getScale()) ? bzVo.getScale() : -1;
            }
        }
        return rule;
    }

    /**
     * 判断计算公式中参数是否都存在结果
     *
     * @param formula   范围
     * @param objectMap 参数
     * @return 是否缺少或者为空
     */
    public static Boolean calculationMapValue(String formula, Map<String, Object> objectMap) {
        boolean flag = Boolean.FALSE;
        Matcher m = MAP_PATTERN.matcher(formula);
        if (!m.find()) {
            flag = Boolean.TRUE;
        }
        Matcher t = MAP_PATTERN.matcher(formula);
        while (t.find()) {
            String key = t.group(0);
            //参数是否包含公式
            if (objectMap.containsKey(key)) {
                Object value = objectMap.get(key);
                //如果 参数内容为空
                if (!StringUtil.isNotNull(value) || StringUtil.isEmpty(value.toString())) {
                    flag = Boolean.TRUE;
                }
            } else {
                flag = Boolean.TRUE;
            }
            //如果判断true则跳出循环
            if (flag) {
                break;
            }
        }
        return flag;
    }

    /**
     * 比较两个字符串
     *
     * @param charString1 字符串1
     * @param charString2 字符串2
     * @return 比对结果
     */
    public static double compareCharacter(String charString1, String charString2) {
        Map<String, Integer> v1 = calculateWord(charString1);
        Map<String, Integer> v2 = calculateWord(charString2);
        double dotProduct = 0.0;
        double magnitude1 = 0.0;
        double magnitude2 = 0.0;

        for (String word : v1.keySet()) {
            if (v2.containsKey(word)) {
                dotProduct += v1.get(word) * v2.get(word);
            }
            magnitude1 += Math.pow(v1.get(word), 2);
        }

        for (String word : v2.keySet()) {
            magnitude2 += Math.pow(v2.get(word), 2);
        }

        magnitude1 = Math.sqrt(magnitude1);
        magnitude2 = Math.sqrt(magnitude2);
        double result = dotProduct / (magnitude1 * magnitude2);
        return Double.parseDouble(ReviseDataFactory.revise(String.valueOf(result), 5, 4, Boolean.FALSE));
    }

    private static Map<String, Integer> calculateWord(String charString) {
        String[] words = charString.toLowerCase().replace(" ", "").split("\\s{0}");
        Map<String, Integer> wordVector = new HashMap<>();
        for (String word : words) {
            wordVector.put(word, wordVector.getOrDefault(word, 0) + 1);
        }
        return wordVector;
    }


    /**
     * 对字符串进行MD5加密
     *
     * @param input 需要加密的字符串
     * @return 加密后的字符串
     */
    public static String toMD5(String input) {
        try {
            // 获取MD5摘要算法的 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 执行哈希计算，将输入的字符串转换为字节数组
            byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            // 将字节数组转换为32位的16进制字符串
            BigInteger bigInt = new BigInteger(1, digest);
            StringBuilder md5Hex = new StringBuilder(bigInt.toString(16));
            // 如果生成的字符串长度不足32位，则前面补0
            while (md5Hex.length() < 32) {
                md5Hex.insert(0, "0");
            }
            return md5Hex.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * 数据对和与对差带入计算相对误差和偏差
     *
     * @param calculationService 计算接口
     * @param samValue           原样值
     * @param qcValue            质控值
     * @param deviationFormula   公式
     * @param diffAvg            对差均值（未修约）
     * @param addAvg             对和均值（未修约）
     * @param isAbs              是否绝对值
     * @return 结果
     */
    public static String diffCalculate(CalculationService calculationService, String samValue, String qcValue,
                                       String deviationFormula, String diffAvg, String addAvg, boolean isAbs) {
        String retStr = "";
        if (isNumber(diffAvg) && isNumber(addAvg)) {
            BigDecimal diff = new BigDecimal(diffAvg);
            BigDecimal add = new BigDecimal(addAvg);
            BigDecimal a = new BigDecimal(samValue);
            BigDecimal b = new BigDecimal(qcValue);
            if (StringUtil.isNotEmpty(deviationFormula)) {
                if (diff.compareTo(BigDecimal.ZERO) != 0) {
                    if ((deviationFormula.contains("a+b") || deviationFormula.contains("[a]+[b]"))
                            && add.compareTo(BigDecimal.ZERO) == 0) {
                        retStr = "无法计算，除数为0";
                    } else {
                        if (a.compareTo(BigDecimal.ZERO) == 0) {
                            retStr = "无法计算，除数为0";
                        }
                    }
                } else {
                    retStr = BigDecimal.ZERO.toString();
                }
                if (!retStr.contains("无法计算")) {
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("a", a);
                    paramMap.put("b", b);
                    deviationFormula = deviationFormula.replace("[a]+[b]", addAvg);
                    deviationFormula = deviationFormula.replace("[b]-[a]", diffAvg);
                    retStr = calculationService.calculationExpression(deviationFormula, paramMap).toString();
                }
                if (isNumber(retStr)) {
                    retStr = new BigDecimal(retStr).multiply(new BigDecimal("100")).round(new MathContext(10, RoundingMode.HALF_EVEN)).toString();
                }
            } else {
                // 没有公式默认 （b-a）/a
                if (diff.compareTo(BigDecimal.ZERO) != 0) {
                    if (a.compareTo(BigDecimal.ZERO) != 0) {
                        if (isAbs){
                            diff = diff.abs();
                        }
                        retStr = (diff.divide(new BigDecimal(samValue), 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
                    } else {
                        retStr = "无法计算";
                    }
                } else {
                    retStr = BigDecimal.ZERO.toString();
                }
            }
        }
        return retStr;
    }
}
