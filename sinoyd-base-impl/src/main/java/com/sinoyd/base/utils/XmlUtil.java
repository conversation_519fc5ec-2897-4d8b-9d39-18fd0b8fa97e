package com.sinoyd.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;


/**
 * Xml工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/04/29
 **/
@Slf4j
public class XmlUtil {

    private static final ResourceLoader resourceLoader = new DefaultResourceLoader();

    /**
     * 读取Xml文件
     *
     * @param path Xml文件路径
     * @return Xml文件字符串
     */
    public static String readXml(String path) {
        Resource resource = resourceLoader.getResource(path);
        try (InputStream inputStream = resource.getInputStream(); BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            return byteArrayOutputStream.toString("UTF-8");
        } catch (Exception e) {
            log.error("加载XML文件失败", e);
            throw new RuntimeException(e);
        }
    }
}
