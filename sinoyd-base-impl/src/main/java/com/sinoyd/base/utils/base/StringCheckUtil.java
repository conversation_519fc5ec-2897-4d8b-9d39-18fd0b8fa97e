package com.sinoyd.base.utils.base;

import com.sinoyd.boot.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串校验工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/20
 */
@Component
@Slf4j
public class StringCheckUtil {

    @Value("${special-str-check.pattern:`~!@#$%^&*+=|{}':;,\\<>/?~！￥%——|‘；：”“’。？}")
    private String regEx;

    @Value("${special-str-check.enable:false}")
    private Boolean enable;

    /**
     * 验证字符串是否包含特殊字符
     *
     * @params str 待验证的字符串
     * @return 是否存在特殊字符
     */
    public Boolean checkSpecialString(String str) {
        log.info("... enable_static: " + this.enable);
        log.info("... str: " + str);
        log.info("... regEx_static: " + this.regEx);
        if (!this.enable){
            return false;
        }
        if  (StringUtil.isEmpty(str)) {
            return false;
        }
        String parttern = "[" + this.regEx + "]";
        Pattern p = Pattern.compile(parttern);
        Matcher m = p.matcher(str);
        return m.find();
    }
}
