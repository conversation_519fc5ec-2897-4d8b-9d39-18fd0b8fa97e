package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;

import java.util.List;
import java.util.Map;

public class QualityBlankMark extends QualityControlKind {

    /**
     * 空白加标类别
     * @return 空白加标类别
     */
    @Override
    public Integer qcTypeValue(){
        return 131072;
    }

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    @Override
    public String qcTypeName() {
        return "空白加标";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "空白加标回收";
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了空白加标样qcCode，原样为oldCode。";
    }

    /**
     * 样品名称
     * @param folderName 原样名称
     * @param qcGrade 质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        String redFolderName = "";
        if (qcGrade.equals(1)) {
            redFolderName = StringUtils.isNotNullAndEmpty(folderName) ? String.format("%s(现场空白加标样)", folderName) : "现场空白加标样";
        } else {
            redFolderName = "空白加标";
        }
        return redFolderName;
    }

    @Override
    public DtoQualityConfig getQualityConfig(){
        return getConfig("QualityBlankMark");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("QualityBlankMark", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
