package com.sinoyd.base.factory.task;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.XmlConfig;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.base.dto.vo.ComparisonReviseVo;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.QualityDivation;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.service.CalculationService;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
public abstract class QualityControlKind {

    protected static List<String> zxList = new ArrayList<>();

    protected static List<String> syList = new ArrayList<>();

    protected static String examLimitValue = "";

    protected static Integer reviseType = 0;

    protected static String configValue = "";

    protected static Integer sign = -1;

    protected static Integer md = -1;

    /**
     * 返回对应的质控类型
     *
     * @return 质控类型
     */
    public abstract Integer qcTypeValue();

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    public abstract String qcTypeName();

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    public abstract String getSampleProperty(Integer qcGrade);

    public abstract Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value);

    /**
     * 质控限值计算
     *
     * @param limitList 质控限值配置
     * @param valueList 数据结果
     * @return 计算结果
     */
    public Map<String, Object> calculateDeviationValue(List<DtoQualityControlLimit> limitList, List<String> valueList) {
        Map<String, Object> map = new HashMap<>();
        QualityDivation qualityDivation = SpringContextAware.getBean(QualityDivation.class);
        Map<String, String> formulaMap = getFormulaMap();
        for (DtoQualityControlLimit controlLimit : limitList) {
            getCalculateData(controlLimit, valueList, map);
            // 先获取质控配置中的偏差公式。为空则获取默认偏差公式
            String deviationFormula = controlLimit.getFormula();
            if (StringUtil.isEmpty(deviationFormula)) {
                //根据质控类型，质控等级获取配置的偏差公式
                deviationFormula = getDeviationFormula(controlLimit.getQcGrade(), controlLimit.getQcType(),
                        controlLimit.getJudgingMethod());
            }
            if (formulaMap.containsKey(deviationFormula)) {
                deviationFormula = formulaMap.get(deviationFormula);
            }
            map.put("deviationFormula", deviationFormula);
            qualityDivation.deviationValue(controlLimit, valueList, map);
//            new QualityDivation().deviationValue(controlLimit, valueList, map);
            //当返回质控限值合格不为空的时候，跳出循环
            if (StringUtil.isNotEmpty(map.get("qcRate").toString())) {
                map.put("limit", controlLimit);
                break;
            }
        }
        return map;
    }

    /**
     * 公式转化
     *
     * @return 公式数据
     */
    private Map<String, String> getFormulaMap() {
        Map<String, String> formulaMap = new HashMap<>();
        formulaMap.put("|(b-a)/a|*100%", "Abs([b]-[a])/[a]");
        formulaMap.put("|(b-a)/(a+b)|*100%", "Abs([b]-[a])/([a]+[b])");
        formulaMap.put("|(b-a)/((a+b)/2)|*100%", "Abs([b]-[a])/(([a]+[b])/2)");
//        formulaMap.put("(b-a)/a*100%", "([b]-[a])/[a]");
        formulaMap.put("(b-a)/(a+b)*100%", "([b]-[a])/([a]+[b])");
        formulaMap.put("(b-a)/((a+b)/2)*100%", "([b]-[a])/(([a]+[b])/2)");
        formulaMap.put("a/b*100%", "[a]/[b]");
        formulaMap.put("(b-a)/a*100%", "([b]-[a])/[a]");
        formulaMap.put("b-a", "([b]-[a])");
        formulaMap.put("|b-a|", "Abs([b]-[a])");
        formulaMap.put("a-b", "([a]-[b])");
        formulaMap.put("lgb-lga", "Log10([b])-Log10([a])");
        formulaMap.put("|lgb-lga|", "Abs(Log10([b])-Log10([a]))");
        formulaMap.put("(lgb-lga)/lga", "(Log10([b])-Log10([a]))/Log10([a])");
        formulaMap.put("(lgb-lga)/(lgb+lga)", "(Log10([b])-Log10([a]))/(Log10([b])+Log10([a]))");
        formulaMap.put("(lgb-lga)/((lgb+lga)/2)", "(Log10([b])-Log10([a]))/((Log10([b])+Log10([a]))/2)");
        formulaMap.put("|lgb-lga|/lga", "Abs(Log10([b])-Log10([a]))/Log10([a])");
        formulaMap.put("|lgb-lga|/(lgb+lga)", "Abs(Log10([b])-Log10([a]))/(Log10([b])+Log10([a]))");
        formulaMap.put("|lgb-lga|/((lgb+lga)/2)", "Abs(Log10([b])-Log10([a]))/((Log10([b])+Log10([a]))/2)");
        formulaMap.put("|b/a|*100%", "Abs([b]/[a])");
        formulaMap.put("|a-(a+b)/2|", "Abs([a]-([a]+[b])/2)");
        formulaMap.put("a-(a+b)/2", "[a]-([a]+[b])/2");
        return formulaMap;
    }

    /**
     * 根据质控限值配置获取配置的偏差公式
     *
     * @param controlLimit 指控限值配置
     * @return 偏差公式
     */
    public String getDeviationFormula(DtoQualityControlLimit controlLimit) {
        return StringUtil.isNotNull(controlLimit) ? getDeviationFormula(controlLimit.getQcGrade(), controlLimit.getQcType(), controlLimit.getJudgingMethod()) : "";
    }

    /**
     * 根据质控类型，质控等级获取配置的偏差公式
     *
     * @param qcGrade 质控等级
     * @param qcType  质控类型
     * @return 偏差公式
     */
    private String getDeviationFormula(Integer qcGrade, Integer qcType,Integer method) {
        //从redis中获取
        RedisTemplate redisTemplate = SpringContextAware.getBean(RedisTemplate.class);
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_DeviationFormula.getValue());
        String formula = (String) redisTemplate.opsForHash().get(key, qcGrade + "_" + qcType + "_" + method);
        if (StringUtil.isNull(formula)) {
            //redis查不到就从数据库中获取
            DtoQualityLimitDisposition formulaLimit = getDeviationFormulaFromDatabase(qcGrade, qcType, method, redisTemplate, key);
            if (StringUtil.isNotNull(formulaLimit)) {
                formula = formulaLimit.getFormula();
            } else {
                //不存在对应质控等级和质控类型的偏差公式，则从redis中获取默认的偏差公式
                formula = (String) redisTemplate.opsForHash().get(key, -1 + "_" + -1);
                if (StringUtil.isNull(formula)) {
                    //redis查不到就从数据库中获取
                    DtoQualityLimitDisposition defaultFormulaLimit = getDeviationFormulaFromDatabase(-1, -1,
                            -1, redisTemplate, key);
                    if (StringUtil.isNotNull(defaultFormulaLimit)) {
                        formula = defaultFormulaLimit.getFormula();
                    }
                }
            }
        }
        return StringUtil.isNotEmpty(formula) ? formula : "";
    }

    /**
     * 从数据库中获取偏差公式对应的质控限值对象
     *
     * @param qcGrade       质控等级
     * @param qcType        质控类型
     * @param method        方法类型
     * @param redisTemplate redis操作模板
     * @param key           键
     * @return 偏差公式对应的质控限值对象
     */
    private DtoQualityLimitDisposition getDeviationFormulaFromDatabase(Integer qcGrade, Integer qcType, Integer method,
                                                                       RedisTemplate redisTemplate, String key) {
        CommonRepository commonRepository = SpringContextAware.getBean(CommonRepository.class);
        Map<String, Object> values = new HashMap<>();
        values.put("qcGrade", qcGrade);
        values.put("qcType", qcType);
        values.put("judgingMethod", method);
        List<DtoQualityLimitDisposition> limitList = commonRepository.find("select a from DtoQualityLimitDisposition a where " +
                "qcGrade = :qcGrade and qcType = :qcType and judgingMethod = :judgingMethod and isAcquiesce = 1", values);
        DtoQualityLimitDisposition limit = limitList.stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(limit)) {
            redisTemplate.opsForHash().put(key, limit.getQcGrade() + "_" + limit.getQcType() + "_" +
                    limit.getJudgingMethod(), limit.getFormula());
        }
        return limit;
    }

    /**
     * 质控限值判定是否合格
     *
     * @param controlLimit 质控限值配置
     * @param value        数据
     * @return 判定结果
     */
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value, CalculationService calculationService) {
        //允许检查项范围
        Boolean passStr = null;
        String allowLimit = controlLimit.getAllowLimitData();
        if (StringUtil.isNotEmpty(allowLimit) && DivationUtils.isNumber(value)) {
            if (DivationUtils.calculationResult(allowLimit, new BigDecimal(value), calculationService)) {
                passStr = true;
            } else {
                passStr = false;
            }
        }
        return passStr;
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    public abstract String getFormt(Map<String, String> map);

    public abstract DtoQualityConfig getQualityConfig();

    public abstract DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList);

    public DtoQualityConfig getComparisonConfig(String code, int type) {
        return getConfig(code, type);
    }

    protected DtoQualityConfig getConfig(String typeName) {
        DtoQualityConfig qualityConfig = new DtoQualityConfig();
        Optional<OrderReviseVO> qcRuleOp = getQualityList().stream().filter(p -> p.getCode().equals(typeName)).findFirst();
        return getDtoQualityConfig(qualityConfig, qcRuleOp);
    }

    protected DtoQualityConfig getConfig(String typeName, List<OrderReviseVO> orderReviseVOList) {
        DtoQualityConfig qualityConfig = new DtoQualityConfig();
        Optional<OrderReviseVO> qcRuleOp = orderReviseVOList.stream().filter(p -> p.getCode().equals(typeName)).findFirst();
        return getDtoQualityConfig(qualityConfig, qcRuleOp);
    }

    private DtoQualityConfig getDtoQualityConfig(DtoQualityConfig qualityConfig, Optional<OrderReviseVO> qcRuleOp) {
        if (qcRuleOp.isPresent()) {
            qualityConfig.setOrderNumber(Integer.valueOf(qcRuleOp.get().getOrder()));
            qualityConfig.setMostSignificance(qcRuleOp.get().getSign());
            qualityConfig.setMostDecimal(qcRuleOp.get().getScale());
            //是否跟随原样 跟：1 不跟：0
            qualityConfig.setIsFollowSample(qcRuleOp.get().getFollowSample().equals("1"));
            //在原样上面还是原样下面， 上面：0 下面：1 == true 是down false 是 up
            qualityConfig.setUpordown(qcRuleOp.get().getSampleFirst().equals("1"));
            //是否关联扩展 是 1，否 0 true 是 关联 false 是 不关联
            qualityConfig.setExtension(qcRuleOp.get().getIsExtend().equals("1"));
        }
        return qualityConfig;
    }

    protected DtoQualityConfig getConfig(String code, int type) {
        DtoQualityConfig qualityConfig = new DtoQualityConfig();
        Optional<ComparisonReviseVo> qcRuleOp = getComparisonList().stream().filter(p -> p.getCode().equals(code) && p.getType().equals(type)).findFirst();
        if (qcRuleOp.isPresent()) {
            qualityConfig.setMostSignificance(qcRuleOp.get().getSign());
            qualityConfig.setMostDecimal(qcRuleOp.get().getScale());
        }
        return qualityConfig;
    }

    public void setZxList(List<String> zxList) {
        QualityControlKind.zxList = zxList;
    }

    public void setSyList(List<String> syList) {
        QualityControlKind.syList = syList;
    }

    public void setExamLimitValue(String examLimitValue) {
        QualityControlKind.examLimitValue = examLimitValue;
    }

    public void setReviseType(Integer reviseType) {
        QualityControlKind.reviseType = reviseType;
    }

    public void setConfigValue(String configValue) {
        QualityControlKind.configValue = configValue;
    }

    public void setSign(Integer sign) {
        QualityControlKind.sign = sign;
    }

    public void setMd(Integer md) {
        QualityControlKind.md = md;
    }

    /**
     * 样品名称
     *
     * @param folderName 原样名称
     *                   `     * @param qcGrade    质控类型
     * @return 样品名称
     */
    public abstract String getRedFolderName(String folderName, Integer qcGrade);

    protected List<OrderReviseVO> getQualityList() {
        XmlConfig xmlConfig = SpringContextAware.getBean(XmlConfig.class);
        return xmlConfig.getQcRulesConfigVO().getQualityReviseVo().getOrderAndRevise();
    }

    protected List<ComparisonReviseVo> getComparisonList() {
        XmlConfig xmlConfig = SpringContextAware.getBean(XmlConfig.class);
        return xmlConfig.getQcRulesConfigVO().getComparisonReviseVo().getComparisonAndRevise();
    }

    protected void getCalculateData(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {

    }

    protected String halfLimit(String value, String examLimitValue, String configValue) {
        //获取小于检出限计算方式参数配置
        if (MathUtil.isNumber(value) && MathUtil.isNumber(examLimitValue)) {
            //检出限大于检测结果
            if (MathUtil.parseString(examLimitValue).compareTo(MathUtil.parseString(value)) > 0) {
                //根据参数值处理小于检出限的数值
                if (StringUtil.isNotEmpty(configValue)) {
                    if (EnumBase.EnumLessExamLimit.检出限一半.getValue().equals(configValue)) {
                        value = (MathUtil.parseString(examLimitValue).divide(new BigDecimal(2))).toString();
                    }
                    if (EnumBase.EnumLessExamLimit.检出限.getValue().equals(configValue)) {
                        value = examLimitValue;
                    }
                    if (EnumBase.EnumLessExamLimit.零.getValue().equals(configValue)) {
                        value = "0";
                    }
                    if (EnumBase.EnumLessExamLimit.检测结果.getValue().equals(configValue)) {
                        value = value;
                    }
                } else {
                    value = (MathUtil.parseString(examLimitValue).divide(new BigDecimal(2))).toString();
                }
            }
        }
        return value;
    }
}
