package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class QualityReplace extends QualityControlKind {

    /**
     * 替代样类别
     * @return 替代样类别
     */
    @Override
    public Integer qcTypeValue(){
        return 8192;
    }

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    @Override
    public String qcTypeName() {
        return "替代";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "替代回收";
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了替代样qcCode，原样为oldCode。";
    }

    /**
     * 样品名称
     * @param folderName 原样名称
     * @param qcGrade 质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        String redFolderName = "";
        if (qcGrade.equals(1)) {
            redFolderName = StringUtils.isNotNullAndEmpty(folderName) ? String.format("%s(实验室替代样)", folderName) : "实验室替代样";
        } else {
            redFolderName = StringUtils.isNotNullAndEmpty(folderName) ? String.format("%s(替代样)", folderName) : "替代样";
        }
        return redFolderName;
    }

    @Override
    public DtoQualityConfig getQualityConfig(){
        return getConfig("QualityReplace");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("QualityReplace", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
