package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 比对数据
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/14
 */
public class JudgeData extends QualityControlKind {

    @Override
    public Integer qcTypeValue() {
        return null;
    }

    @Override
    public String qcTypeName() {
        return "比对";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "比对";
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return null;
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return null;
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return null;
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return null;
    }

    @Override
    protected void getCalculateData(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        if ("gasJudgeData".equals(controlLimit.getJudgeDataType())) {
            try {
                valueList.clear();
                //在线均值
                String zxAvg = MathUtil.calculateAvg(zxList);
                //实验室均值
                String syAvg = MathUtil.calculateAvg(syList);
                if (MathUtil.isNumber(zxAvg) && MathUtil.isNumber(syAvg)) {
                    CalculateService calculateService = SpringContextAware.getBean(CalculateService.class);
                    if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(reviseType)) {
                        syAvg = halfLimit(syAvg, examLimitValue, configValue);
                        zxAvg = halfLimit(zxAvg, examLimitValue, configValue);
                    }
                    syAvg = calculateService.revise(sign, md, syAvg);
                    zxAvg = calculateService.revise(sign, md, zxAvg);
                    valueList.add(syAvg);
                    valueList.add(zxAvg);
                    valueList.add(syAvg);
                    // 数据对差 对和 赋值
                    this.setDiffList(controlLimit, calculateService);
                }
                if (EnumBase.EnumJudgingMethod.相对准确度.getValue().equals(controlLimit.getJudgingMethod())) {
                    //计算相对准确度，数据需要做一次处理
                    map.put(IBaseConstants.ONLINE_DATA_LIST, zxList);
                    map.put(IBaseConstants.LABORATORY_DATA_LIST, syList);
                }
            } catch (Exception ex) {

            }
        }
    }

    /**
     * 数据对差 对和 赋值
     *
     * @param controlLimit     质控限值配置
     * @param calculateService 计算相关接口
     */
    private void setDiffList(DtoQualityControlLimit controlLimit, CalculateService calculateService) {

        //对差集合
        List<String> diList = new ArrayList<>();
        // 对和集合
        List<String> addList = new ArrayList<>();
        for (int i = 0; i < syList.size(); i++) {
            if (zxList.size() > i) {
                diList.add(MathUtil.subtract(zxList.get(i), syList.get(i)));
                addList.add(add(zxList.get(i), syList.get(i)));
            }
        }
        // 数据对差和 对和均值
        String diffAvg = MathUtil.calculateAvg(diList);
        String addAvg = MathUtil.calculateAvg(addList);
        if (MathUtil.isNumber(diffAvg) && MathUtil.isNumber(addAvg)) {
            if (!EnumBase.EnumJudgingMethod.相对偏差.getValue().equals(controlLimit.getJudgingMethod()) &&
                    !EnumBase.EnumJudgingMethod.相对误差.getValue().equals(controlLimit.getJudgingMethod())) {
                diffAvg = calculateService.revise(sign, md, diffAvg);
                addAvg = calculateService.revise(sign, md, addAvg);
            }
            List<String> diffList = new ArrayList<>();
            diffList.add(diffAvg);
            diffList.add(addAvg);
            controlLimit.setDiscrepancyList(diffList);
        }
    }


    /**
     * 数据求和
     *
     * @param number1 数据1
     * @param number2 数据2
     * @return 和
     */
    private String add(String number1, String number2) {
        BigDecimal one = new BigDecimal(number1);
        BigDecimal two = new BigDecimal(number2);
        return one.add(two).toPlainString();
    }
}
