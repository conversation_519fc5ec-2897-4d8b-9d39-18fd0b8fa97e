package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;

import java.util.List;
import java.util.Map;

/**
 * 洗涤剂
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/30
 */
public class QualitySampleCopy extends QualityControlKind {
    @Override
    public Integer qcTypeValue() {
        return 16;
    }

    @Override
    public String qcTypeName() {
        return "原样加原样";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "原样加原样";
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了原样加原样qcCode，原样为oldCode。";
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return folderName;
    }

    @Override
    public DtoQualityConfig getQualityConfig(){
        return getConfig("SampleCopy");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("SampleCopy", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}